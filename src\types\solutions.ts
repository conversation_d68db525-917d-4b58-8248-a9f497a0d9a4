export interface Solution {
  initial_thoughts: string[]
  thought_steps: string[]
  description: string
  code: string
}

export interface SolutionsResponse {
  [key: string]: Solution
}

export interface ProblemStatementData {
  problem_statement: string
  input_format: {
    description: string
    parameters: any[]
  }
  output_format: {
    description: string
    type: string
    subtype: string
  }
  complexity: {
    time: string
    space: string
  }
  test_cases: any[]
  validation_type: string
  difficulty: string
  question_type?: 'coding' | 'mcq' | 'guess-output' // Indicates whether this is a coding question, MCQ, or guess-output question
  default_function_signature?: string // Default function signature from platforms like LeetCode
}

// Interface for Multiple Choice Questions
export interface MCQData {
  question: string
  options: MCQOption[]
  correct_answer?: string // Optional during extraction, required after processing
  explanation?: string // Optional during extraction, required after processing
}

export interface MCQOption {
  id: string // A, B, C, D, etc.
  text: string
}

// Extended problem statement data that includes MCQ information
export interface ExtendedProblemStatementData extends ProblemStatementData {
  mcq_data?: MCQData
}
