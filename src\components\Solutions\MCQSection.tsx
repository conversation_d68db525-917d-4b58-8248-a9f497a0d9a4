import React, { useState, useEffect } from "react";
import { MCQData } from "../../types/solutions";
import { Prism as SyntaxHighlighter } from "react-syntax-highlighter";
import { dracula } from "react-syntax-highlighter/dist/esm/styles/prism";

interface MCQSectionProps {
  title: string;
  mcqData: MCQData | null;
  isLoading: boolean;
}

// Helper function to render text with code blocks
const renderTextWithCodeBlocks = (text: string) => {
  if (!text) return null;

  // Split text by code blocks
  const parts = text.split(/(```[\s\S]*?```)/g);

  return parts.map((part, index) => {
    // Check if this part is a code block
    if (part.startsWith('```') && part.endsWith('```')) {
      // Extract language and code
      const lines = part.split('\n');
      const firstLine = lines[0];
      const language = firstLine.replace('```', '').trim() || 'text';
      const code = lines.slice(1, -1).join('\n');

      return (
        <div key={index} className="my-3">
          <SyntaxHighlighter
            language={language === 'c' ? 'c' : language}
            style={dracula}
            customStyle={{
              margin: 0,
              padding: "0.75rem",
              fontSize: "12px",
              backgroundColor: "rgba(22, 27, 34, 0.8)",
              borderRadius: "6px",
            }}
            wrapLongLines={true}
          >
            {code}
          </SyntaxHighlighter>
        </div>
      );
    } else {
      // Regular text
      return (
        <span key={index}>
          {part}
        </span>
      );
    }
  });
};

export const MCQSection: React.FC<MCQSectionProps> = ({
  title,
  mcqData,
  isLoading,
}) => {
  // Set the correct answer as selected by default
  const [selectedOption, setSelectedOption] = useState<string | null>(null);
  // Always show the answer by default
  const [showAnswer, setShowAnswer] = useState(true);

  // When mcqData changes, update the selected option to the correct answer
  useEffect(() => {
    if (mcqData && mcqData.correct_answer) {
      setSelectedOption(mcqData.correct_answer);
    }
  }, [mcqData]);

  if (isLoading) {
    return (
      <div className="space-y-2">
        <h2 className="text-[13px] font-medium text-white tracking-wide">
          {title}
        </h2>
        <div className="mt-4 flex">
          <p className="text-xs bg-gradient-to-r from-gray-300 via-gray-100 to-gray-300 bg-clip-text text-transparent animate-pulse">
            Analyzing MCQ question...
          </p>
        </div>
      </div>
    );
  }

  if (!mcqData) {
    return (
      <div className="space-y-2">
        <h2 className="text-[13px] font-medium text-white tracking-wide">
          {title}
        </h2>
        <div className="text-[13px] leading-[1.4] text-gray-100">
          No MCQ data available.
        </div>
        {process.env.NODE_ENV === 'development' && (
          <div className="mt-2 p-2 bg-black/30 rounded text-xs text-gray-400">
            <div>MCQ Debug Info:</div>
            <div>mcqData: {JSON.stringify(mcqData)}</div>
            <div>isLoading: {isLoading ? 'Yes' : 'No'}</div>
          </div>
        )}
      </div>
    );
  }

  // Ensure options is an array with the correct format
  // Replace the original mcqData.options with our normalized version
  if (!Array.isArray(mcqData.options) && typeof mcqData.options === 'object') {
    mcqData.options = Object.entries(mcqData.options).map(([id, text]) => ({
      id,
      text: String(text)
    }));
  } else if (!Array.isArray(mcqData.options)) {
    // If options is not an array or object, set it to an empty array
    mcqData.options = [];
    console.error("MCQ options is not in the expected format:", mcqData.options);
  }

  // This function is kept for compatibility but won't be used since answers are shown by default
  const handleOptionSelect = (optionId: string) => {
    setSelectedOption(optionId);
  };

  const handleReset = () => {
    setSelectedOption(null);
    setShowAnswer(true); // Keep showing the answer after reset
  };

  // No longer need handleCheckAnswer as we always show the answer

  return (
    <div className="space-y-4">
      <h2 className="text-[13px] font-medium text-white tracking-wide">
        {title}
      </h2>

      {/* Question */}
      <div className="text-[13px] leading-[1.4] text-gray-100 max-w-[600px] mb-4">
        <div className="font-medium mb-2">
          {renderTextWithCodeBlocks(mcqData.question)}
        </div>
      </div>

      {/* Options */}
      <div className="space-y-2">
        {Array.isArray(mcqData.options) && mcqData.options.length > 0 ? (
          mcqData.options.map((option) => (
            <div
              key={option.id}
              className={`p-3 rounded-md cursor-pointer transition-colors ${
                selectedOption === option.id
                  ? "bg-blue-500/20 border border-blue-500/50"
                  : "bg-white/5 border border-white/10 hover:bg-white/10"
              } ${
                showAnswer && option.id === mcqData.correct_answer
                  ? "bg-green-500/20 border border-green-500/50"
                  : ""
              } ${
                showAnswer &&
                selectedOption === option.id &&
                option.id !== mcqData.correct_answer
                  ? "bg-red-500/20 border border-red-500/50"
                  : ""
              }`}
              onClick={() => !showAnswer && handleOptionSelect(option.id)}
            >
              <div className="flex items-start">
                <div className="w-6 shrink-0 font-medium text-white/80">
                  {option.id}.
                </div>
                <div className="text-white/90">{option.text}</div>
              </div>
            </div>
          ))
        ) : (
          <div className="p-3 bg-white/5 border border-white/10 rounded-md">
            <p className="text-white/70">No options available for this MCQ question.</p>
          </div>
        )}
      </div>

      {/* Action Buttons */}
      <div className="flex gap-3 mt-4">
        <button
          className="px-4 py-2 rounded-md text-sm font-medium bg-white/10 text-white hover:bg-white/20 transition-colors"
          onClick={handleReset}
        >
          Reset
        </button>
      </div>

      {/* Explanation (always shown) */}
      <div className="mt-4 p-4 bg-white/5 border border-white/10 rounded-md">
        <h3 className="text-[13px] font-medium text-white mb-2">
          Explanation:
        </h3>
        <div className="text-[13px] leading-[1.4] text-gray-100">
          {mcqData.explanation ? renderTextWithCodeBlocks(mcqData.explanation) : "No explanation available."}
        </div>
      </div>
    </div>
  );
};

export default MCQSection;
