# MCQ Code Extraction Test

## Test Case 1: C Memory Allocation Question

**Expected Input (from screenshot):**
```
Consider the following program, where are i, j and k are stored in memory?

int i;
int main() {
    int j;
    int *k = (int*) malloc(sizeof(int));
}

A. i, j and *k are stored in stack segment
B. i and j are stored in stack segment. *k is stored on heap.
C. i is stored in BSS part of data segment, j is stored in stack segment. *k is stored on heap.
D. j is stored in BSS part of data segment, i is stored in stack segment. *k is stored on heap.
```

**Expected Extraction Output:**
```json
{
  "problem_statement": "Consider the following program, where are i, j and k are stored in memory?\n\n```c\nint i;\nint main() {\n    int j;\n    int *k = (int*) malloc(sizeof(int));\n}\n```",
  "question_type": "mcq",
  "mcq_data": {
    "question": "Consider the following program, where are i, j and k are stored in memory?\n\n```c\nint i;\nint main() {\n    int j;\n    int *k = (int*) malloc(sizeof(int));\n}\n```",
    "options": [
      {"id": "A", "text": "i, j and *k are stored in stack segment"},
      {"id": "B", "text": "i and j are stored in stack segment. *k is stored on heap."},
      {"id": "C", "text": "i is stored in BSS part of data segment, j is stored in stack segment. *k is stored on heap."},
      {"id": "D", "text": "j is stored in BSS part of data segment, i is stored in stack segment. *k is stored on heap."}
    ]
  }
}
```

## Changes Made

1. **Updated extraction prompts** in `ProcessingHelper.ts` for all API providers (OpenAI, Gemini, Anthropic, OpenRouter)
   - Added specific instructions to extract code blocks with markdown formatting
   - Emphasized importance for questions about code output, memory allocation, variable scope, etc.

2. **Enhanced MCQSection component** in `src/components/Solutions/MCQSection.tsx`
   - Added `react-syntax-highlighter` import
   - Created `renderTextWithCodeBlocks()` function to parse and render markdown code blocks
   - Updated question and explanation display to use the new rendering function
   - Added proper syntax highlighting for code blocks

3. **Added debugging logs** in `ProcessingHelper.ts`
   - Added extraction debug logging to see what's being extracted from screenshots

## Testing Instructions

1. Take a screenshot of an MCQ with code (like the memory allocation question)
2. Process it with the application
3. Check the console logs for extraction debug info
4. Verify that the MCQ displays with properly formatted code blocks
5. Ensure the code has syntax highlighting and proper indentation

## Expected Behavior

- Code blocks in MCQ questions should be extracted with proper markdown formatting
- The UI should render code blocks with syntax highlighting
- Code structure and indentation should be preserved
- Multiple programming languages should be supported (C, C++, Java, Python, etc.)
